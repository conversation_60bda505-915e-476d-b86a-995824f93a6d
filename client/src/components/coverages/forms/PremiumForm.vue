<template>
  <div class="_fw">
    <q-tab-panels class="_panel" :model-value="tab">
      <q-tab-panel class="_panel" name="rateType">
        <div class="q-px-sm">
          <div class="__l">Rate Type</div>

          <q-list separator>
            <q-item clickable @click="setTab('rateByAge')">
              <q-item-section avatar>
                <q-icon v-if="form.rateType === 'rateByAge'" color="green" name="mdi-checkbox-marked"></q-icon>
                <q-icon v-else name="mdi-checkbox-blank-outline" color="ir-grey-5"></q-icon>
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ rateTypes.rateByAge }}</q-item-label>
                <q-item-label caption>Premium is the total of household persons - ACA style</q-item-label>
              </q-item-section>
            </q-item>
            <q-expansion-item inset-level="1" hide-expand-icon dense>
              <template v-slot:header>
                <q-item class="_fw _panel">
                  <q-item-section avatar>
                    <q-icon v-if="form.rateType === 'fixedRates'" color="green" name="mdi-checkbox-marked"></q-icon>
                    <q-icon v-else name="mdi-checkbox-blank-outline" color="ir-grey-5"></q-icon>
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ rateTypes.fixedRates }}</q-item-label>
                    <q-item-label caption>Explicit fixed rates "Single", "Plus Spouse", etc.</q-item-label>
                  </q-item-section>
                </q-item>
              </template>
              <div class="_fw q-pa-md">
                <div class="font-3-4r text-ir-mid tw-six">Select Breakpoint Type</div>
                <q-radio @update:modelValue="setTab('fixedRates')" v-model="form.rateBreak" val="graduated"
                         label="Graduated (each age)"></q-radio>
                <q-radio @update:modelValue="setTab('fixedRates')" v-model="form.rateBreak" val="breakpoint"
                         label="Breakpoint (ie: 31-40, 41-50, etc.)"></q-radio>
              </div>
            </q-expansion-item>
            <q-item clickable @click="setTab('flatPremium')">
              <q-item-section avatar>
                <q-icon v-if="form.rateType === 'flatPremium'" color="green" name="mdi-checkbox-marked"></q-icon>
                <q-icon v-else name="mdi-checkbox-blank-outline" color="ir-grey-5"></q-icon>
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ rateTypes.flatPremium }}</q-item-label>
                <q-item-label caption>Same premium for all ages by household size</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </div>
      </q-tab-panel>
      <q-tab-panel class="_panel" name="rates">
        <div class="__c">

          <q-chip color="ir-bg2" class="q-mb-sm">
            <span class="q-mr-sm">Rate Type: <b>{{ rateTypes[form.rateType] }}</b></span>
            <q-btn dense flat size="sm" color="red" icon="mdi-close" @click="tab = 'rateType'"></q-btn>
          </q-chip>

          <div class="_fw q-py-sm" v-if="estimated">
            <div class="font-1r text-ir-deep tw-five">These rates were estimated based on partial information you provided. Please review and <q-chip color="primary" class="tw-six text-white" icon-right="mdi-check-circle" clickable @click="confirmEstimate()">Confirm</q-chip><q-chip color="ir-bg2" clickable @click="cancelEstimate"><span class="q-mr-sm">Cancel Estimate</span><q-icon color="red" name="mdi-close"></q-icon></q-chip></div>
          </div>

          <q-tab-panels class="_panel" animated v-model="rateTab" transition-prev="jump-up" transition-next="jump-down">
            <q-tab-panel class="_panel" name="flatPremium">
              <div class="_form_grid">
                <div class="_form_label">Flat Premium (no age-banded rates)</div>
                <div class="q-pa-sm">
                  <div class="__table">
                    <table class="__inp">
                      <thead>
                      <tr>
                        <th>Single</th>
                        <th>+Spouse</th>
                        <th>+Child</th>
                        <th>+2</th>
                        <th>+3</th>
                        <th>Family</th>
                      </tr>
                      </thead>
                      <tbody>
                      <tr>
                        <td v-for="(k, idx) in rateKeys" :key="`rate-${idx}`" :id="`${age}.${k}`"
                            @click="setFocus([age, k])">
                          <input
                              class="text-right"
                              :id="`inp-${age}-${k}`"
                              :value="dollarString((form.flatPremium || {})[k], '$', 0)"
                              @input="setFlatPremium(k, $event.target.value)"
                          >
                        </td>
                      </tr>
                      </tbody>
                    </table>
                  </div>

                  <div class="q-py-md"
                       v-if="!form.flatPremium || Object.keys(rateKeys).some(a => !form.flatPremium[a])">

                    <!--                    ADD ESTIMATE-->

                  </div>
                </div>
              </div>
            </q-tab-panel>
            <q-tab-panel class="_panel" name="rateByAge">

              <div class="_form_grid">

                <div class="_form_label">Age Banded Rates</div>
                <div class="q-pa-sm">
                  <q-chip color="transparent" clickable @click="rateTab = 'enter'">
                    <span class="q-mr-sm">Enter Rates</span>
                    <q-icon color="blue" name="mdi-pencil"></q-icon>
                  </q-chip>
                </div>

                <div class="_form_label">
                  Apply base rates by default
                </div>
                <div class="q-pa-sm">
                  <q-checkbox
                      :model-value="!!form.baseDefault"
                      @update:model-value="form.baseDefault = $event, autoSave"
                      label="Apply base rates if no more specific local rate applies"
                  ></q-checkbox>
                </div>

                <div class="_form_label">
                  Volume Discount
                </div>
                <div class="q-pa-sm">
                  <div class="_fw">
                    <div class="q-pb-sm font-7-8r text-ir-deep">Discount factor for number of persons covered. 1 = 100%
                      of
                      standard price, 0.9 =
                      90% of standard price, etc. multiplied by the number of persons
                    </div>

                    <table>
                      <tbody>
                      <tr>
                        <td>Enrolled Persons</td>
                        <td>Discount Factor</td>
                      </tr>
                      <tr v-for="(k, i) in Object.keys(form.multiDiscount)" :key="`k-${i}`">
                        <td>{{ k }} {{ k === '1' ? 'Person' : 'People' }}</td>
                        <td>
                          <money-input
                              class="mxw100 cursor-pointer"
                              dense
                              filled
                              borderless
                              @update:model-value="autoSave"
                              :decimal="2"
                              prefix=""
                              v-model="form.multiDiscount[k]">
                          </money-input>
                        </td>
                      </tr>
                      </tbody>
                    </table>
                    <q-item dense clickable @click="form.multiDiscount[Object.keys(form.multiDiscount).length + 1] = 1">
                      <q-item-section avatar>
                        <q-icon name="mdi-plus" color="primary"></q-icon>
                      </q-item-section>
                      <q-item-section>
                        <q-item-label>New Row</q-item-label>
                      </q-item-section>
                    </q-item>
                  </div>
                </div>

                <slot name="rate-bottom"></slot>

              </div>

            </q-tab-panel>
            <q-tab-panel class="_panel" name="enter">
              <q-btn flat icon="mdi-chevron-left" color="primary" @click="rateTab = 'rateByAge'"></q-btn>
              <div class="_fw q-py-md">
                <q-chip clickable color="ir-bg2" @click="estimate()">
                  <span class="tw-five q-mr-sm">Fill Empty Rates</span>
                  <ai-logo size="15px"></ai-logo>
                </q-chip>
              </div>
              <rate-upload @update:model-value="autoSave" v-model="form.rateByAge"></rate-upload>
            </q-tab-panel>
            <q-tab-panel class="_panel" name="fixedRates">

              <div class="row items-center">
                <q-btn dense flat no-caps @click="newAge">
                  <span class="q-mr-sm">Add Age</span>
                  <q-icon name="mdi-plus" color="primary"></q-icon>
                </q-btn>
                <q-space></q-space>
                <remove-proxy-btn icon="mdi-close" color="purple" label="Clear All" flat rounded size="sm" @remove="clearFixedRates" remove-label="Clear All Rates?"></remove-proxy-btn>
              </div>
              <div class="__table">
                <table class="__inp">
                  <thead>
                  <tr>
                    <th>Age</th>
                    <th>Single</th>
                    <th>+Spouse</th>
                    <th>+Child</th>
                    <th>+2</th>
                    <th>+3</th>
                    <th>Family</th>
                  </tr>
                  </thead>
                  <tbody>
                  <tr v-for="(age, i) in Object.keys(form.fixedRates || {})" :key="`age-${i}`" :id="`row-${age}`">
                    <td>
                      <input
                          style="width: 3em"
                          class="text-right"
                          :id="`age-${age}`"
                          :value="ageFocus === age ? focusAge : age"
                          @focus="setFocusAge(age)"
                          @input="setAge($event.target.value)"
                          @blur="finishAge(age)"
                      >
                    </td>
                    <td v-for="(k, idx) in rateKeys" :key="`rate-${idx}`" :id="`${age}.${k}`"
                        @click="setFocus([age, k])">
                      <input
                          class="text-right"
                          :id="`inp-${age}-${k}`"
                          @paste="handlePaste(age, k, $event)"
                          @keydown="senseTab($event)"
                          :value="dollarString((form.fixedRates[age] || {})[k], '$', 0)"
                          @input="setFixedRate(age, k, $event.target.value)"
                      >
                    </td>
                    <td style="width: 1%">
                      <q-btn @click="removeAge(age)" dense flat size="xs" color="red" icon="mdi-close"></q-btn>
                    </td>
                  </tr>
                  </tbody>
                </table>

                <div class="_fw q-py-md">
                  <q-chip clickable color="ir-bg2" @click="estimate()">
                    <span class="tw-five q-mr-sm">Fill Empty Rates</span>
                    <ai-logo size="15px"></ai-logo>
                  </q-chip>
                </div>
              </div>
            </q-tab-panel>
          </q-tab-panels>


        </div>

        <div class="__c">
          <div class="__l">Smoker Premium</div>
          <div class="q-pa-sm">
            <money-input
                dense
                class="mxw200"
                filled
                @update:model-value="autoSave"
                prefix=""
                v-model="form.smokerFactor"
                :decimal="2"
                label="Smoker Multiplier (ex: 1.56)"
            ></money-input>
          </div>
        </div>
      </q-tab-panel>
    </q-tab-panels>


  </div>
</template>

<script setup>
  import MoneyInput from 'components/common/input/MoneyInput.vue';
  import RateUpload from 'components/coverages/forms/RateUpload.vue';
  import AiLogo from 'src/utils/icons/AiLogo.vue';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';

  import {computed, ref, watch} from 'vue';
  import {$errNotify, dollarString} from 'src/utils/global-methods';
  import {multiCellArray} from 'src/utils/csv';
  import {estimatePremium} from 'components/compare/group/utils/estimate-premium';


  const rateKeys = ['single', 'plus_spouse', 'plus_child', 'plus_child__2', 'plus_child__3', 'family'];
  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: Object,
    allowEstimate: Boolean
  })

  const rateTypes = {
    'rateByAge': 'Age Banded - Per Person',
    'fixedRates': 'Age Banded - By Household',
    'flatPremium': 'Flat Premium - No Age Banding'
  }

  const formFn = (defs) => {
    return {
      multiDiscount: {
        1: 1,
        2: 1,
        3: 1,
        4: 1
      },
      smokerFactor: 1,
      rateByAge: {},
      ...defs
    }
  };
  const form = ref(formFn());

  const tab = ref('rateType')

  const lastFixed = ref({});
  const rateTab = ref('rateByAge');
  const setTab = (val) => {
    if (val === 'rateByAge') {
      form.value.rateType = 'rateByAge'
      lastFixed.value = { ...form.value.fixedRates || {} };
      form.value.fixedRates = {}
      rateTab.value = 'rateByAge';
    } else if (val === 'fixedRates') {
      form.value.rateType = 'fixedRates'
      if(Object.keys(lastFixed.value).length) form.value.fixedRates = { ...lastFixed.value }
      // form.value.fixedRates = lastFixed.value;
      rateTab.value = 'fixedRates'
    } else {
      form.value.rateType = 'flatPremium';
      rateTab.value = 'flatPremium'
    }
    tab.value = 'rates'
  }

  const newAge = () => {
    if(!form.value.fixedRates) form.value.fixedRates = {}
    const ages = Object.keys({ 18: {}, ...form.value.fixedRates })
    const oldest = Number(ages[ages.length - 1] || 18);
    if (oldest < 99) form.value.fixedRates[oldest + 1] = form.value.fixedRates[oldest] || {}
  }

  const emitTo = ref()
  const emitUp = () => {
    if (emitTo.value) clearTimeout(emitTo.value)
    emitTo.value = setTimeout(() => {
      form.value.breakpointAges = Object.keys(form.value.fixedRates || {}).map(a => Number(a))
      emit('update:model-value', form.value)
    }, 500)
  }

  const to = ref(undefined);
  const autoSave = () => {
    if (to.value) clearTimeout(to.value);
    to.value = setTimeout(() => {
      emitUp()
    }, 1000)
  }

  const ageFocus = ref();
  const focusAge = ref();
  const setFocusAge = (age) => {
    focusAge.value = age;
    ageFocus.value = age;
  }
  const setAge = (nv) => {
    focusAge.value = nv
  }
  const finishAge = (age) => {
    if (focusAge.value && Number(focusAge.value) !== Number(age)) {
      if (!form.value.fixedRates[focusAge.value]) {
        form.value.fixedRates[focusAge.value] = { ...form.value.fixedRates[age] }
        delete form.value.fixedRates[age];
      }
    }
    ageFocus.value = undefined;
    focusAge.value = undefined;
    emitUp()
  }

  const removeAge = (age) => {
    delete form.value.fixedRates[age]
    autoSave()
  }


  const focused = ref([])
  const hasFocused = ref(false);
  const lastTab = ref(false);
  const setFocus = (val, lt) => {
    focused.value = val;
    hasFocused.value = true;
    const el = document.getElementById(`inp-${val[0]}-${val[1]}`);
    if (el) el.focus();
    lastTab.value = lt;
  }

  const clearFixedRates = () => {
    form.value.fixedRates = {}
  }

  const setFixedRate = (age, key, value) => {
    if (!(form.value.fixedRates || {})[age]) form.value.fixedRates[age] = {};
    form.value.fixedRates[age][key] = Number(String(value).replace(/[^\d.]/g, '') || 0);
    emitUp()
  }
  const setFlatPremium = (key, value) => {
    if (!form.value.flatPremium) form.value.flatPremium = {};
    form.value.flatPremium[key] = Number(String(value).replace(/[^\d.]/g, '') || 0);
    // console.log('saved flat premium', key, value, form.value.flatPremium);
    autoSave('premium.flatPremium')
  }

  const senseTab = (e) => {
    if (e.keyCode === 9) {
      e.preventDefault();
      let age = Number(focused.value[0] || -1);
      let key = focused.value[1];
      let idx = rateKeys.indexOf(key);
      if (idx >= rateKeys.length - 1) {
        if (form.value.fixedRates[`${age + 1}`]) {
          age += 1;
          idx = 0;
        } else if (age < 99) {
          form.value.fixedRates[`${age + 1}`] = {};
          age++
          idx = 0;
        }
      } else idx++
      setFocus([age, rateKeys[idx]], true);
    }
  };

  const handlePaste = (age, key, evt) => {
    evt.preventDefault()
    const d = multiCellArray(evt.clipboardData.getData('text').trim());
    const index = rateKeys.indexOf(key);
    for (let i = index; i < rateKeys.length; i++) {
      setFixedRate(age, i, d[0][i - index])
    }
    for (let i = 1; i < d.length; i++) {
      // if (!Array.isArray(form.value.fixedRates[i + row])) csvData.value[i + row] = [];
      for (let idx = index; idx < Math.max(rateKeys.length, Math.min(rateKeys.length, (d[i] || []).length + index)); idx++) {
        setFixedRate(i + Number(age), rateKeys[idx], d[i][idx - index])
      }
    }
  }

  const estimated = ref(false);
  const beforeEstimate = ref({});
  const confirmEstimate = () => {
    estimated.value = false;
    emitUp()
  }
  const cancelEstimate = () => {
    form.value = { ...beforeEstimate.value }
    estimated.value = false;
  }
  const estimate = () => {
    beforeEstimate.value = form.value;
    try {
      const v = estimatePremium(form.value)
      console.log('premium estimated', v);
      form.value = v;
      estimated.value = true;
    } catch (e) {
      console.error(`Could not estimate premium: ${e.message}`)
      $errNotify('Could not estimate premium')
    }
  }

  const breakpointAges = computed(() => form.value.breakpointAges);

  watch(breakpointAges, (nv, ov) => {
    if (nv?.length && nv.length !== ov?.length) {
      const obj = {};
      for (const age of nv) {
        obj[age] = form.value.fixedRates[age];
      }
      form.value.fixedRates = obj;
    }
  }, { immediate: true })

  watch(() => props.modelValue, (nv) => {
    if (nv) {
      form.value = formFn(nv);
      if (nv.rateType) {
        tab.value = 'rates'
        if (rateTab.value !== 'enter') rateTab.value = nv.rateType;
      } else if (nv.fixedRates) {
        rateTab.value = 'fixedRates'
      }
    }
  }, { immediate: true })
</script>

<style lang="scss" scoped>
  .__c {
    overflow-x: hidden;
    width: 100%;
    border-radius: 8px;
    //border: solid 1px var(--ir-light);
    padding: 20px 10px;
    margin: 10px 0;
  }

  .__l {
    font-size: 1rem;
    font-weight: 600;
    margin: 10px;
  }

  table {
    border-collapse: collapse;

    td {
      border-bottom: solid .2px #999;
      padding: 5px 10px;
    }
  }

  .__rb {
    margin: 20px 10px;
    border-top: solid .3px #999;
    border-bottom: solid .3px #999;
    padding: 10px 0;
  }

  .__table {
    width: 100%;
    overflow-x: scroll;
    max-width: min(90vw, 630px);

    .__inp {
      width: 100%;
      border-collapse: collapse;

      tr {
        th {
          padding: 2px 4px;
          text-align: center;
          //border: solid 1px #999;
          font-size: var(--text-xxs);
          color: var(--q-ir-grey-7);
        }

        td {
          font-size: var(--text-xxs);
          padding: 4px 8px;
          text-align: right;
          border: solid 1px #999;
          background: white;

          &:first-child {
            background: var(--q-ir-grey-2) !important;
          }
        }

      }
    }

    input {
      border: none;
      width: 4em;
      box-sizing: content-box;
      padding: 0px;
      background-color: transparent;

      &:focus {
        border: none;
        outline: none;
      }
    }
  }


</style>
