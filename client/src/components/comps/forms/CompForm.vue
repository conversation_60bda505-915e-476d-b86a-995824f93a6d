<template>
  <div class="_fw q-py-md">
    <q-tab-panels class="_panel" :model-value="removeTab" animated>
      <q-tab-panel class="_panel" :name="false">
        <q-tabs v-if="form._id" no-caps align="left" indicator-color="primary" v-model="tab">
          <q-tab name="form" label="Compensation"></q-tab>
          <q-tab name="recruit" label="Recruiting"></q-tab>
        </q-tabs>
        <q-tab-panels class="_panel" v-model="tab" animated transition-prev="slide-down" transition-next="slide-up">

<!--          COMP FORM-->
          <q-tab-panel class="_panel" name="form">
            <div class="_f_g">
              <template v-if="service === 'cams'">
                <div class="_f_l _f_chip">Comp Package</div>
                <div class="q-pa-sm">
                  <q-chip color="ir-bg2" clickable>
                    <span class="q-mr-sm">{{ parent.name || 'Select Package' }}</span>
                    <q-icon name="mdi-menu-down"></q-icon>

                    <q-popup-proxy>
                      <div class="w500 mw100 q-pa-sm">
                        <q-input v-model="search.text" dense filled>
                          <template v-slot:prepend>
                            <q-icon name="mdi-magnify"></q-icon>
                          </template>
                        </q-input>
                        <q-list separator>
                          <q-item v-for="(comp, i) in c$.data" :key="`comp-${i}`" clickable @click="setComp(comp)">
                            <q-item-section>
                              <q-item-label>{{ comp.name }}</q-item-label>
                            </q-item-section>
                            <q-item-section side>
                              <pay-chip color="ir-green-1" :model-value="comp"></pay-chip>
                            </q-item-section>
                          </q-item>
                        </q-list>
                      </div>
                    </q-popup-proxy>
                  </q-chip>
                </div>
              </template>
              <template v-if="service === 'cams' || form?.person || personId">
                <div class="_f_l _f_chip">For</div>
                <div class="q-pa-sm">
                  <q-chip
                      color="ir-bg2"
                      clickable
                  >
                    <default-avatar :model-value="person"></default-avatar>
                    <span class="q-mr-sm">{{ person.name || 'Select Person' }}</span>
                    <q-icon v-if="!person._id" name="mdi-menu-down"></q-icon>
                    <q-btn dense flat v-else size="sm" color="red" icon="mdi-close" @click="delete form.person"></q-btn>
                    <q-popup-proxy v-model="pplMenu">
                      <div class="w500 mw100 q-pa-sm">
                        <q-input v-model="pplSearch.text" dense filled>
                          <template v-slot:prepend>
                            <q-icon name="mdi-magnify"></q-icon>
                          </template>
                        </q-input>
                        <q-list separator>
                          <q-item v-for="(p, i) in p$.data"
                                  :key="`p-${i}`"
                                  clickable
                                  @click="setForm('person', p._id)"
                          >
                            <q-item-section avatar>
                              <default-avatar :model-value="p"></default-avatar>
                            </q-item-section>
                            <q-item-section>
                              <q-item-label>{{p.name}}</q-item-label>
                              <q-item-label caption>{{p.email}}</q-item-label>
                            </q-item-section>
                          </q-item>
                        </q-list>
                      </div>
                    </q-popup-proxy>
                  </q-chip>
                </div>
              </template>
              <div class="_f_l _f_chip">Name</div>
              <div class="q-pa-sm">
                <q-input v-model="form.name" placeholder="Name Package..." @update:model-value="autoSave('name')"></q-input>
              </div>

              <div class="_f_l _f_chip">Amount</div>
              <div class="q-pa-sm">
                <money-input @update:model-value="autoSave('amount')" placeholder="Enter Pay Amount" v-model="form.amount"
                             decimal="2"></money-input>
              </div>

              <div class="_f_l _f_chip">Type</div>


              <div class="_form_grid">
                <div class="_form_label">Interval</div>
                <div class="q-pa-sm">
                  <interval-chip @update:model-value="autoSave('interval')" v-model="form.interval" picker></interval-chip>
                </div>
                <template v-if="service === 'cams'">
                  <div class="_form_label">Hire Date</div>
                  <div class="q-pa-sm">
                    <hire-date edit emit-up :model-value="form" @update:date="setForm('hireDate', $event)"></hire-date>
                  </div>
                </template>
                <template v-if="!!form.interval">
                  <div class="_form_label">Class</div>
                  <div class="q-pa-sm">
                    <class-picker @update:model-value="autoSave('class')" v-model="form.class"></class-picker>
                  </div>
                  <div class="_form_label">Time</div>
                  <div class="q-pa-sm">
                    <div class="_fw q-py-sm">Estimated Weekly Hours</div>
                    <div class="mw100p">
                      <money-input dense filled @update:model-value="autoSave('estHours')" v-model="form.estHours" prefix=""
                                   :decimal="1"></money-input>
                    </div>
                  </div>
                </template>
              </div>


              <div class="_f_l _f_chip">
                Terms
              </div>
              <div class="q-pa-sm">
                <q-editor
                    :id="form.terms || 'Editor'"
                    :model-value="form.terms || ''"
                    @update:model-value="setForm('terms', $event)"
                    min-height="140px"
                    placeholder="Compensation terms (simple there is a separate contract)..."
                    dense
                    flat
                    :content-style="{ borderRadius: '10px' }"
                    content-class="bg-ir-grey-2 q-px-md"
                    :toolbar="[['bold', 'italic', 'strike', 'underline', 'code', 'link']]"
                ></q-editor>
              </div>

              <div class="_f_l _f_chip">Extras</div>
              <div class="q-pa-sm">
                <extras-form :org="fullOrg" v-model="form.extras" @update:model-value="autoSave('extras')"></extras-form>
              </div>

            </div>
            <div class="_fw" v-if="removable && form?._id">
              <q-btn no-caps flat @click="removeTab = true">
                <span class="q-mr-sm">Remove Comp Arrangement</span>
                <q-icon name="mdi-delete" color="red"></q-icon>
              </q-btn>
            </div>
            <div class="row justify-end q-py-lg" v-if="!form?._id">
              <q-btn no-caps class="tw-six" icon-right="mdi-content-save" :label="`Save Comp Package${person._id ? ' for ' + person.name : ''}`" @click="save"
                     color="primary" push></q-btn>
            </div>
          </q-tab-panel>
          <q-tab-panel class="_panel" name="recruit">

<!--            RECRUITING-->
            <comp-prospects :model-value="form"></comp-prospects>
          </q-tab-panel>
        </q-tab-panels>

      </q-tab-panel>

<!--      REMOVE FORM-->
      <q-tab-panel class="_panel" :name="true">
        <div class="q-py-sm row">
          <q-btn dense flat icon="mdi-close" color="red" @click="removeTab = false"></q-btn>
        </div>
        <cam-termination :ids="[form._id]"></cam-termination>
      </q-tab-panel>
    </q-tab-panels>

  </div>
</template>

<script setup>
  import IntervalChip from 'src/components/comps/btns/IntervalChip.vue';
  import ExtrasForm from 'src/components/comps/forms/ExtrasForm.vue';
  import MoneyInput from 'src/components/common/input/MoneyInput.vue';
  import ClassPicker from 'components/comps/forms/ClassPicker.vue';
  import HireDate from 'components/comps/cams/cards/HireDate.vue';
  import CamTermination from 'components/comps/cams/forms/CamTermination.vue';
  import PayChip from 'components/comps/cards/PayChip.vue';
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';
  import CompProspects from 'components/comps/forms/CompProspects.vue';

  import {HForm, HSave} from 'src/utils/hForm';
  import {computed, ref} from 'vue';
  import {dualStore} from '../utils';
  import {usePpls} from 'stores/ppls';
  import {HQuery} from 'src/utils/hQuery';
  import {HFind} from 'src/utils/hFind';
  import {idGet} from 'src/utils/id-get';
  import {omitFieldsForCopy} from 'components/comps/utils/copy';
  import {useOrgs} from 'stores/orgs';

  const pplsStore = usePpls();
  const orgStore = useOrgs();

  const emit = defineEmits(['update:model-value'])
  const props = defineProps({
    org: { required: true },
    modelValue: Object,
    service: { default: 'comps' },
    compId: String,
    personId: String,
    removable: Boolean
  })

  const removeTab = ref(false);
  const { store, compStore } = dualStore(computed(() => props.service));

  const { item: fullOrg } = idGet({
    store: orgStore,
    value: computed(() => props.org)
  })

  const tab = ref('form');

  const formFn = (defs) => {
    return {
      amount: 30,
      class: 'ee',
      interval: 'hour',
      person: props.personId,
      comp: props.compId,
      ...defs
    }
  }

  const { form, save } = HForm({
    store: store.value,
    formFn,
    value: computed(() => props.modelValue),
    beforeFn: (val) => {
      if (props.compId) val.comp = props.compId;
      if (props.personId) val.person = props.personId;
      if (!val.org) val.org = fullOrg.value._id
      return val;
    },
    afterFn: (val) => {
      if (val?._id) emit('update:model-value', val);
    }
  })

  const { searchQ, search } = HQuery({})
  const { h$: c$ } = HFind({
    store: compStore,
    pause: computed(() => props.service !== 'cams'),
    params: computed(() => {
      return {
        query: {
          org: fullOrg.value._id,
          ...searchQ.value
        }
      }
    })
  })

  const pplMenu = ref(false);
  const { search: pplSearch, searchQ:pplSearchQ } = HQuery({})

  const { h$: p$ } = HFind({
    store: pplsStore,
    pause: computed(() => !pplMenu.value),
    params: computed(() => {
      return {
        query: {
          inGroups: { $in: Object.keys(fullOrg.value?.groups || {}).map(a => fullOrg.value.groups[a]) },
          ...pplSearchQ.value
        }
      }
    })
  })

  const { item: person } = idGet({
    store: pplsStore,
    value: computed(() => form.value.person)
  })

  const { item: parent } = idGet({
    store: compStore,
    value: computed(() => form.value.comp)
  })

  const { setForm, autoSave } = HSave({
    form,
    pause: computed(() => !form.value?._id),
    store
  })

  const setComp = (c) => {
    setForm('comp', c._id)
    for (const k in c) {
      if (!omitFieldsForCopy.includes(k)) {
        if (!form.value[k]) form.value[k] = c[k]
      }
    }
  }
</script>

<style lang="scss" scoped>

</style>
