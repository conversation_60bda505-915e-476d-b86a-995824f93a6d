<template>
  <q-page class="_bg_ow">
    <div class="row justify-center">
      <div class="_cent pd4 pw2 bg-white mnh80">

        <div class="_fw">

          <q-tabs :model-value="tab" no-caps align="left" class="text-p10 bg-p0 br5" active-class="tw-six" indicator-color="primary"
                  @update:model-value="setTab">
            <q-tab name="active">
              <span>Active</span>
            </q-tab>
            <q-tab name="packages">
              <span>Packages</span>
            </q-tab>
            <q-tab name="prospects">
              <span>Prospects</span>
            </q-tab>
            <q-tab name="agreements">
              <span>Agreements</span>
            </q-tab>
          </q-tabs>

          <q-tab-panels class="_panel" v-model="tab" animated>
            <q-tab-panel class="_panel" name="active">
              <div class="row items-center q-py-md">
                <div class="font-1r tw-six">Employee Compensation</div>
                <q-btn flat text-color="primary" icon="mdi-plus" @click="addDialog = true"></q-btn>
              </div>
              <q-input class="q-my-md" v-model="search.text" placeholder="Search by comp package...">
                <template v-slot:prepend>
                  <q-icon name="mdi-magnify"></q-icon>
                </template>
              </q-input>
              <div class="_fw q-py-md">

                <q-chip
                    color="ir-bg2"
                    clickable
                >
                  <span class="q-mr-sm">Select Person</span>
                  <q-icon name="mdi-menu-down"></q-icon>
                  <q-popup-proxy v-model="pplMenu">
                    <div class="w500 mw100 q-pa-sm">
                      <q-input v-model="pplSearch.text" dense filled>
                        <template v-slot:prepend>
                          <q-icon name="mdi-magnify"></q-icon>
                        </template>
                      </q-input>
                      <q-list separator>
                        <q-item v-for="(p, i) in p$.data"
                                :key="`p-${i}`"
                                clickable
                                @click="togglePerson(p)"
                        >
                          <q-item-section avatar>
                            <default-avatar :model-value="p"></default-avatar>
                          </q-item-section>
                          <q-item-section>
                            <q-item-label>{{p.name}}</q-item-label>
                            <q-item-label caption>{{p.email}}</q-item-label>
                          </q-item-section>
                        </q-item>
                      </q-list>
                    </div>
                  </q-popup-proxy>
                </q-chip>
                <q-chip color="ir-bg2" v-for="(p, i) in persons" :key="`prsn-${i}`">
                  <default-avatar :model-value="p"></default-avatar>
                  <span>{{p.name}}</span>
                  <q-btn dense flat size="sm" class="q-ml-sm" color="red" icon="mdi-close" @click="togglePerson(p)"></q-btn>
                </q-chip>

                <div class="row items-center q-py-sm">
                  <comp-chip
                      color="ir-bg2"
                      multiple
                      v-model="compFilter"
                      :options="h$.data"
                      :outline="compFilter?.length"
                      :removable="compFilter?.length"
                      @remove="compFilter = []"
                  ></comp-chip>

                </div>
                <div class="row items-center">
                  <div>
                    <interval-chip
                        :removable="!!interval"
                        :outline="!!interval"
                        @remove="interval = ''" picker
                        v-model="interval"
                    ></interval-chip>
                  </div>
                  <div>
                    <class-picker
                        :outline="!!eeClass"
                        :removable="!!eeClass"
                        @remove="eeClass = ''"
                        v-model="eeClass"
                    ></class-picker>
                  </div>
                </div>

              </div>

              <q-table
                  :rows="c$.data"
                  :columns="columns"
              >
                <template v-slot:header="scope">
                  <q-th auto-width>
                    <q-checkbox
                        :model-value="selected?.length === pageRecordCount"
                        @update:model-value="toggleAll"
                    ></q-checkbox>
                  </q-th>
                  <q-th
                      v-for="col in scope.cols"
                      :key="col.name"
                      :props="scope"
                  >
                    {{ col.label }}
                  </q-th>
                </template>
                <template v-slot:body="scope">
                  <q-tr :props="scope">
                    <q-td>
                      <q-checkbox
                          @update:model-value="toggleSelected(scope.row)"
                          :model-value="isSelected(scope.row)"
                      ></q-checkbox>
                    </q-td>
                    <q-td v-for="(col, i) in scope.cols" :key="`td-${i}`">
                      <component :is="col.component" v-bind="col.attrs(scope.row)"></component>
                    </q-td>
                  </q-tr>
                </template>
                <template v-slot:bottom>
                  <div class="row items-center _fw">
              <span>{{
                  ((pagination.currentPage * camsLimit) - camsLimit) + 1
                }} - {{ pageRecordCount }} of {{ c$.total }}</span>
                    <q-space></q-space>
                    <q-pagination
                        @update:model-value="c$.toPage($event)"
                        :model-value="pagination.currentPage"
                        :min="1"
                        :max="pagination.pageCount"
                        direction-links
                        boundary-numbers
                    ></q-pagination>
                  </div>
                </template>
              </q-table>
            </q-tab-panel>
            <q-tab-panel class="_panel" name="packages">
              <div class="row items-center q-py-md">
                <div class="font-1r tw-six">Compensation Packages</div>
                <q-btn flat text-color="primary" icon="mdi-plus" @click="addDialog = true"></q-btn>
              </div>
              <q-input class="q-my-md" v-model="search.text" placeholder="Search by comp package...">
                <template v-slot:prepend>
                  <q-icon name="mdi-magnify"></q-icon>
                </template>
              </q-input>
              <div class="__comp_grid">
                <div v-for="(comp, i) in h$.data" :key="`comp-${i}`" class="__c _fw">
                  <div class="t-r-a">
                    <q-btn dense flat icon="mdi-dots-vertical" @click="selected = [comp]"></q-btn>
                  </div>
                  <comp-card
                      editing
                      @input-value="search.text = $event"
                      :model-value="comp"
                  ></comp-card>
                </div>
              </div>
            </q-tab-panel>

            <q-tab-panel class="_panel" name="prospects">
              <div class="_fw">
                <org-prospects :orG="org"></org-prospects>
              </div>
            </q-tab-panel>
            <q-tab-panel class="_panel" name="agreements">
              <div class="_fw">
                <comp-contract-builder :org-id="org._id"></comp-contract-builder>
              </div>
            </q-tab-panel>
          </q-tab-panels>
        </div>

      </div>
    </div>

    <common-dialog setting="right" @update:model-value="closeDrawer" :model-value="drawer">
      <div class="_fw q-pa-md bg-white">
        <comp-form
            v-if="addDialog || selected.length === 1"
            :service="tab === 'packages' ? 'comps' : 'cams'"
            :org="org._id"
            @update:model-value="addComp"
            :model-value="selected[0]"
            removable
        ></comp-form>
        <q-slide-transition>
          <div v-if="selected?.length > 1" class="_fw">
            <q-tab-panels class="_panel" animated :model-value="removeTab">
              <q-tab-panel class="_panel" :name="false">
                <div class="_form_grid">
                  <div class="_form_label">Remove</div>
                  <div class="q-pa-sm">
                    <q-btn flat no-caps @click="removeTab = true">
                      <span class="q-mr-sm">Remove {{selected.length}} positions</span>
                    </q-btn>
                  </div>
                  <div class="_form_label">Modify</div>
                  <div class="q-pa-sm">
                    <money-input dense filled class="mw200" :decimal="2" prefix="" suffix="%" v-model="modifier"></money-input>
                    <div class="q-pa-sm">
                      <remove-proxy-btn two-step v-if="modifier" remove-label="Confirm you want to modify ${selected.length} positions?" :icon="undefined" @remove="modifyAll"
                      >
                        <span class="q-mr-sm">Confirm</span>
                        <q-icon color="primary" name="mdi-check-circle"></q-icon>
                      </remove-proxy-btn>
                    </div>
                  </div>
                </div>
              </q-tab-panel>
              <q-tab-panel class="_panel" :name="true">
                <div class="q-py-sm row">
                  <q-btn dense flat icon="mdi-close" color="red" @click="removeTab = false"></q-btn>
                </div>
                <cam-termination :ids="selectedIds"></cam-termination>

              </q-tab-panel>
            </q-tab-panels>

          </div>
        </q-slide-transition>
      </div>
    </common-dialog>
  </q-page>
</template>

<script setup>
  import CompForm from 'src/components/comps/forms/CompForm.vue';
  import CompChip from 'components/comps/cards/CompChip.vue';
  import TdChip from 'components/common/tables/TdChip.vue';
  import CompCard from 'src/components/comps/cards/CompCard.vue';
  import PayChip from 'components/comps/cards/PayChip.vue';
  import HireDate from 'components/comps/cams/cards/HireDate.vue';
  import IntervalChip from 'components/comps/btns/IntervalChip.vue';
  import ClassPicker from 'components/comps/forms/ClassPicker.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import CompContractBuilder from 'components/comps/contracts/CompContractBuilder.vue';
  import MoneyInput from 'components/common/input/MoneyInput.vue';
  import CamTermination from 'components/comps/cams/forms/CamTermination.vue';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';
  import OrgProspects from 'components/comps/cams/prospects/OrgProspects.vue';

  import {useComps} from 'src/stores/comps';
  import {computed, ref, watch} from 'vue';
  import {HFind} from 'src/utils/hFind';
  import {HQuery} from 'src/utils/hQuery';
  import {useCams} from 'stores/cams';
  import {usePpls} from 'stores/ppls';
  import {$errNotify} from 'src/utils/global-methods';
  import {useRoute, useRouter} from 'vue-router';
  import {idGet} from 'src/utils/id-get';
  import {useOrgs} from 'stores/orgs';
  import {useEnvStore} from 'stores/env';
  import {contextItems} from 'layouts/utils/context-items';


  const route = useRoute();
  const router = useRouter();

  const compStore = useComps();
  const camsStore = useCams();
  const pplsStore = usePpls();
  const orgStore = useOrgs();

  const envStore = useEnvStore();
  const { getOrgId } = contextItems(envStore);

  const { item:org } = idGet({
    store: orgStore,
    value: getOrgId
  })

  const tab = ref('packages');
  const removeTab = ref(false);

  const addDialog = ref(false);

  const { search, searchQ } = HQuery({})
  const { h$, init } = HFind({
    store:compStore,
    limit: ref(25),
    params: computed(() => {
      return {
        query: {
          org: org.value._id,
          ...searchQ.value
        }
      }
    })
  })

  const compFilter = ref([]);
  const eeClass = ref('');
  const interval = ref('');
  const persons = ref([]);
  const personIds = computed(() => persons.value.map(a => a._id));
  const camsQuery = computed(() => {
    const q = { active: { $ne: false } };
    if (compFilter.value?.length) q.comp = { $in: compFilter.value.map(a => a._id) }
    if (eeClass.value) q.class = eeClass.value;
    if (interval.value) q.interval = interval.value;
    if (persons.value.length) q.person = { $in: personIds.value }
    return q;
  })

  const togglePerson = (p) => {
    const idx = personIds.value.indexOf(p._id);
    if(idx > -1) persons.value.splice(idx, 1);
    else persons.value.push(p);
  }

  const camsLimit = ref(50);

  const { h$: c$, pagination } = HFind({
    pause: computed(() => tab.value !== 'active'),
    store: camsStore,
    limit: camsLimit,
    params: computed(() => {
      return {
        runJoin: { cams_person: true },
        query: {
          org: org.value?._id,
          ...camsQuery.value,
          ...searchQ.value
        }
      }
    })
  })

  const selected = ref([]);
  const selectedIds = computed(() => selected.value?.map(a => a._id))
  const drawer = computed(() => {
    return addDialog.value || !!selected.value?.length
  })
  const closeDrawer = (val) => {
    if (!val) {
      selected.value = [];
      addDialog.value = false;
    }
  }

  const isSelected = (val) => {
    return selectedIds.value?.includes(val._id);
  }
  const toggleSelected = (val) => {
    const idx = selectedIds.value.indexOf(val._id);
    if (idx > -1) selected.value.splice(idx, 1);
    else selected.value.push(val);
  }
  const toggleAll = (val) => {
    if (!val) selected.value = [];
    else selected.value = [...c$.data];
  }

  const pageRecordCount = computed(() => {
    return Math.min(c$.total, pagination.value.currentPage * camsLimit.value)
  });

  const pplMenu = ref(false);
  const { search: pplSearch, searchQ: searchPpls } = HQuery({})

  const { h$: p$ } = HFind({
    store: pplsStore,
    pause: computed(() => !pplMenu.value),
    params: computed(() => {
      return {
        query: {
          ...searchPpls.value,
          inOrgs: { $in: [org.value._id] }
        }
      }
    })
  })

  const addComp = () => {
    addDialog.value = false;
    init()
  }

  const modifier = ref(0);

  const modifyAll = async () => {
    if(!modifier.value) return $errNotify('Must enter a modifier');
    for(const c of selected.value){
      await camsStore.patch(c._id, {$inc: { amount: c.amount + (c.amount * (modifier.value / 100)) }})
          .catch(err => {
            $errNotify(`Error updating ${c.name}: ${err.message}`)
            console.error(`Error updating ${c.name}: ${err.message}`)
          })
    }
  }


  const columns = computed(() => {
    return [
      {
        name: 'Person',
        component: TdChip,
        attrs: (row) => {
          return {
            chipAttrs: {
              color: 'white',
              label: row._fastjoin?.person?.name
            }
          }
        }
      },
      {
        name: 'Role',
        component: TdChip,
        attrs: (row) => {
          return {
            chipAttrs: {
              color: 'white',
              label: row.name
            }
          }
        }
      },
      {
        name: 'Pay',
        component: PayChip,
        attrs: (row) => {
          return {
            service: 'cams',
            modelValue: row
          }
        }
      },
      {
        name: 'Hire Date',
        component: HireDate,
        attrs: (row) => {
          return {
            edit: true,
            modelValue: row
          }
        }
      },
    ].map(a => {
      return {
        ...a,
        label: a.name,
        sortable: true,
        align: 'left',
        field: a.name
      };
    });
  })

  const setTab = (val) => {
    tab.value = val;
    const { params } = route;
    const { tab: rTab } = params || { tab: val };
    if (rTab !== tab.value) {
      const { href } = router.resolve({ ...route, params: { tab: val } })
      window.history.pushState({}, '', href);
    }
  }

  const rp = computed(() => route.params.tab);
  watch(rp, (nv) => {
    if (!nv) tab.value = 'packages'
    else if (nv !== tab.value) tab.value = nv
  }, { immediate: true })
</script>

<style lang="scss" scoped>

  .__lg {
    padding: 0 15px;
  }

  .__l {
    border-right: solid 1px var(--q-p3);
  }


  .__comp_grid {
    display: grid;
    grid-template-rows: repeat(auto-fill, auto);
    grid-template-columns: repeat(auto-fit, minmax(250px, 350px));
    grid-gap: 10px;
    width: 100%;
    padding: 10px;
  }

  .__c {
    position: relative;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 -1px 6px rgba(0, 0, 0, .1);
    background: white;
  }

  @media screen and (max-width: 800px) {
    .__comp_grid {
      grid-template-columns: repeat(auto-fit, minmax(250px, 280px));
    }
  }

  @media screen and (max-width: 550px) {
    .__comp_grid {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
  }
</style>
