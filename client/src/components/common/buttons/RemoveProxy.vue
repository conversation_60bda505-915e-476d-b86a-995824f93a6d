<template>
  <q-popup-proxy v-model="dialog" :breakpoint="twoStep ? 500000 : undefined">
    <div class="q-pa-md bg-ir-bg1 text-ir-text">
      <div class="tw-five">
        <span v-if="removeLabel" v-html="removeLabel"></span>
        <span v-else>Remove {{ name || 'this' }}?</span>
      </div>

      <div v-if="twoStep" class="_fw q-py-md">
        <div class="_fw text-center q-pb-sm">
          <div class="font-3-4r">Enter phrase to confirm</div>
          <div class="font-1r tw-six">{{ phrase }}</div>
        </div>
        <q-input dense filled placeholder="Enter Phrase Here" v-model="confirm"></q-input>
      </div>

      <div class="row justify-end q-pt-sm">
        <q-btn no-caps flat size="sm" @click="dialog = false">
          <span>{{ noLabel }}</span>
          <q-icon class="q-ml-sm" color="red" name="mdi-close"></q-icon>
        </q-btn>
        <q-btn :disable="twoStep && confirm !== phrase" no-caps flat size="sm" @click="remove">
          <span>{{ yesLabel }}</span>
          <q-icon class="q-ml-sm" color="green" name="mdi-check"></q-icon>
        </q-btn>
      </div>
    </div>
  </q-popup-proxy>
</template>

<script setup>
  import {onMounted, ref} from 'vue';

  const emit = defineEmits(['remove']);
  const props = defineProps({
    name: String,
    removeLabel: String,
    twoStep: Boolean,
    yesLabel: { default: 'Yes' },
    noLabel: { default: 'Cancel' }
  })

  const letters = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'];
  const dialog = ref(false);
  const confirm = ref('');
  const phrase = ref('ekqo');

  onMounted(() => {
    if (props.twoStep) {
      let ph = '';
      for (let i = 0; i < 4; i++) {
        ph += letters[Math.round(Math.random() * letters.length)];
      }
      phrase.value = ph;
    }
  })

  const remove = () => {
    emit('remove');
    dialog.value = false;
  }
</script>

<style lang="scss" scoped>

</style>
