<template>
  <q-input
      type="text"
      v-bind="{
    modelValue: mv,
    label: ein ? 'Tax ID' : 'Social Security Number',
    error: !!error,
    errorMessage: error,
    ...$attrs,
    class: `${$attrs.class || ''} ${modelValue && hide ? 'pss' : ''}`
      }"
      @update:model-value="checkInput"
  >
    <template v-slot:prepend>
      <q-btn flat color="ir-blue-10" dense size="sm" :icon="hide ? 'mdi-eye' : 'mdi-eye-off'"
             @click="hide = !hide"></q-btn>
    </template>
    <template v-slot:append>
      <slot name="append">
        <secure-badge></secure-badge>
      </slot>
    </template>
  </q-input>
</template>

<script setup>
  import SecureBadge from 'src/utils/ux/SecureBadge.vue';

  import {computed, ref, watch} from 'vue';

  const emit = defineEmits(['update:model-value'])
  const props = defineProps({
    modelValue: { required: true },
    ein: Boolean
  })

  const hide = ref(true);
  const input = ref('')
  const mv = computed(() => {
    const v = input.value?.split('-').join('');
    if (!v) return undefined;
    if (hide.value) return v;
    else {
      if (props.ein) return `${v.substring(0, 2)}-${v.substring(2, 10)}`
      else return `${v.substring(0, 3)}-${v.substring(3, 5)}-${v.substring(5, 10)}`
    }
  })

  const checkInput = (val) => {
    input.value = val;
    // console.log('check input', val, val.split('-').join('').length)
    if (val.split('-').join('').length === 9) {
      // console.log('here');
      emit('update:model-value', val.split('-').join(''));
    }
  }
  const error = ref('');

  watch(() => props.modelValue, (nv, ov) => {
    if (nv && nv !== ov) input.value = nv;
  }, { immediate: true })
</script>

<style lang="scss" scoped>
</style>
