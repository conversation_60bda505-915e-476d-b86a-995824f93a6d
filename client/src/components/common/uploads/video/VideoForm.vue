<template>
  <div class="_fw">
    <div class="row">
      <q-tabs dense align="left" no-caps v-if="e$.total" v-model="tab" indicator-color="primary">
        <q-tab name="list">
          <span class="font-3-4r">Your Videos</span>
        </q-tab>
        <q-tab name="form">
          <span class="font-3-4r">Add New</span>
        </q-tab>
      </q-tabs>
    </div>
    <q-tab-panels animated class="_panel" :model-value="useTab">
      <q-tab-panel class="_panel" name="list">
        <q-input @focus="searchFocus = true" class="q-mt-sm" placeholder="Search Uploads..." dense filled v-model="search.text">
          <template v-slot:prepend>
            <q-icon name="mdi-magnify"></q-icon>
          </template>
        </q-input>
        <q-slide-transition>
        <q-list v-if="searchFocus" dense separator>
          <q-item v-for="(ul, i) in e$.data" :key="`ul-${i}`" class="_fw">
            <q-item-section avatar>
              <q-icon name="mdi-video-box" color="grey-5"></q-icon>
            </q-item-section>
            <q-item-section>
              <q-item-label>
                {{ ul.name || ul.info?.name || `Uploaded: ${formatDate(ul.createdAt, 'MM/DD/YYYY')}` }}
              </q-item-label>
            </q-item-section>
            <q-item-section side>
              <div class="flex items-center">
                <q-btn dense flat>
                  <q-icon name="mdi-eye" color="primary" size="14px"></q-icon>
                  <span v-if="$q.screen.gt.sm" class="font-3-4r tw-six q-ml-sm">VIEW</span>
                  <q-popup-proxy>
                    <div class="__vid">
                      <div class="t-r-a">
                        <remove-proxy-btn dense flat size="sm" color="red" icon="mdi-close" @remove="removeUpload(ul)"
                                      name="Upload"></remove-proxy-btn>
                      </div>
                      <q-video
                          ratio="1.7777"
                          :src="ul.url"
                      ></q-video>
                    </div>
                  </q-popup-proxy>
                </q-btn>
                <q-btn dense flat no-caps color="p6" @click="selectUpload(ul)">
                  <q-icon name="mdi-check" color="primary" size="14px"></q-icon>
                  <span v-if="$q.screen.gt.sm" class="font-3-4r tw-six q-ml-sm">SELECT</span>
                  <q-tooltip>Select Upload</q-tooltip>
                </q-btn>
              </div>
            </q-item-section>
          </q-item>

        </q-list>
        </q-slide-transition>
      </q-tab-panel>
      <q-tab-panel class="_panel" name="form">

        <div v-if="allowUrl" class="q-py-sm">

          <q-input :error="!!error?.code" :error-message="error.message" no-error-icon :hint="`Supported platforms: ${Object.keys(allowedVideoPlatforms).join(', ')}`" label="Paste URL" dense filled v-model="url" @blur="addUrl">
            <template v-slot:prepend>
              <q-icon v-if="!urlLoading" name="mdi-link-box" color="accent"></q-icon>
              <q-spinner v-else color="accent"></q-spinner>
            </template>
          </q-input>
        </div>

        <div class="__vid q-my-md">
          <video-uploader
              @remove="emit('remove')"
              @update="emit('update:model-value', $event)"
              :display-url="useUrl"
              v-bind="{
                log,
                allowTypes,
                id,
                storage
              }"
          ></video-uploader>

        </div>
      </q-tab-panel>
    </q-tab-panels>
  </div>
</template>

<script setup>
  import VideoUploader from 'components/common/uploads/video/VideoUploader.vue';
  import RemoveProxyBtn from 'components/common/buttons/RemoveProxyBtn.vue';

  import {formatDate} from 'src/utils/date-utils';
  import {computed, ref, watch} from 'vue';
  import {loginPerson} from 'stores/utils/login';
  import {HQuery} from 'src/utils/hQuery';
  import {HFind} from 'src/utils/hFind';
  import {useUploads} from 'stores/uploads';
  import {allowedVideoPlatforms, handleVideoUrl} from 'components/common/uploads/video/utils';
  const { login } = loginPerson()

  const store = useUploads();

  const emit = defineEmits(['remove', 'update:model-value']);
  const props = defineProps({
    allowUrl: Boolean,
    existingIds: Array,
    log: Boolean,
    allowTypes: { default: 'video/mp4,video/x-m4v,video/*' },
    id: { type: String, default: 'video-form' },
    storage: { default: 'bunny' },
    displayUrl: { required: false },
    modelValue: { required: false }
  })

  const url = ref('');

  const searchFocus = ref(false)

  watch(() => props.modelValue, (nv) => {
    if(nv && nv.url && !nv.uploadId) url.value = nv.url;
  }, { immediate: true });

  const query = computed(() => {
    let q = { ['createdBy.login']: login.value?._id }
    if (props.existingIds?.length) q = { $or: [q, { _id: { $in: props.existingIds } }] }
    return q;
  })
  const { search, searchQ } = HQuery({ keys: ['name', 'info.name'], query })
  const { h$: e$ } = HFind({
    store,
    params: computed(() => {
      return {
        query: { video: true, ...searchQ.value }
      }
    }),
    pause: computed(() => !login.value?._id)
  })
  const tab = ref('list');
  const useTab = computed(() => {
    if (e$.total || search.value.text) return tab.value;
    else return 'form'
  })

  const useUrl = computed(() => props.displayUrl || props.modelValue?.url || '');
  const urlLoading = ref(false);
  const error = ref({});
  const addUrl = async () => {
    if(url.value && url.value !== props.modelValue?.url) {
      console.log('adding url');
      urlLoading.value = true;
      error.value = {}
      try {
        const urlRes = await handleVideoUrl(url.value);
        if (urlRes.code !== 200) error.value = urlRes;
        else {
          emit('update:model-value', { url: urlRes.url, ...urlRes.data })
        }
      } catch (err) {
        console.error(`Error handling video url: ${err.message}`)
      } finally {
        urlLoading.value = false;
      }
    }
  }

  watch(() => props.modelValue, (nv) => {
    if (nv) tab.value = 'form';
  }, { immediate: true })

  const removeUpload = (u) => {
    store.remove(u._id)
    searchFocus.value = false;
  }

  const selectUpload = (u) => {
    searchFocus.value = false
    const { fileId, info, storage, url, _id } = u
    emit('update:model-value', {
      uploadId: _id,
      fileId,
      info,
      storage,
      url
    })
  }
</script>

<style lang="scss" scoped>
  .__vid {
    position: relative;
    height: 180px;
    width: 320px;
    max-width: 100%;
    max-height: 100%;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 6px rgba(0, 0, 0, .15);
  }
</style>
