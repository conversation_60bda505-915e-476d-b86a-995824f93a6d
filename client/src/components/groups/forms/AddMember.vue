<template>
  <div class="_fw q-pa-md">
    <q-tab-panels v-model="panel" animated class="_panel">
      <q-tab-panel class="_panel" name="base">

        <div class="text-weight-bold font-7-8r q-py-sm">Choose from other groups</div>
        <q-select :model-value="undefined" :options="h$.data" use-input @input-value="search.text = $event" @focus="pauseSearch = false" filled label="Select members">
          <template v-slot:option="scope">
            <q-item clickable @click="chooseMember(scope.opt)">
              <q-item-section>
                <q-item-label>{{scope.opt.name}}</q-item-label>
                <q-item-label caption>{{scope.opt.email}}</q-item-label>
              </q-item-section>
            </q-item>
          </template>
          <template v-slot:append v-if="loading">
            <q-spinner color="primary"></q-spinner>
          </template>
        </q-select>
        <div class="text-weight-bold q-mt-md font-7-8r q-py-sm">Create/Invite new members</div>

        <q-btn flat no-caps @click="panel = 'upload'">
          <span class="q-mr-sm">Upload Sheet</span>
          <q-icon color="primary" name="mdi-upload"></q-icon>
        </q-btn>
        <q-btn flat no-caps @click="panel='input'" label="Input Member" icon-right="mdi-plus"></q-btn>

      </q-tab-panel>
      <q-tab-panel class="_panel" name="upload">
        <div class="row q-pb-sm">
          <q-btn icon="mdi-chevron-left" flat dense size="sm" @click="panel = 'base'"></q-btn>
        </div>
        <csv-upload
            :response="response"
            headers="name,firstName,lastName,email,phone,dob,ssn,hireDate,comp"
            :required="requiredFields"
            @ready="uploadList"
            @clear="response = {}"
            :example-data="exampleData"
        >
          <template v-slot:response="scope">
            <div class="_fw text-center font-1r">
              <p>New people created - {{ scope.response.added }}</p>
              <p>Existing people matched from this list - {{ scope.response.existing?.total }}</p>
              <p>Data errors - <span class="text-red text-weight-bold">{{ scope.response.errors?.length || 0 }}</span> &nbsp;(see
                below)</p>
            </div>
          </template>
          <template v-slot:bottom>
            <div class="_fw text-grey-8 row items-center no-wrap __gr">
              <div class="font-1-1-4r q-pa-md">
                ⚠️
              </div>
              <div class="tw-five font-3-4r">There is a limit of 1,000 employees per
                sheet. If you have more than 1,000 employees, please break them up into 1,000 person increments - or send us the data and we'll upload it.</div>
            </div>
          </template>
        </csv-upload>
      </q-tab-panel>
      <q-tab-panel class="_panel" name="input">
        <div class="row q-pb-sm">
          <q-btn icon="mdi-chevron-left" flat dense size="sm" @click="panel = 'base'"></q-btn>
        </div>
        <member-input :org-id="org._id" :group-id="modelValue?._id"></member-input>
      </q-tab-panel>
    </q-tab-panels>


  </div>
</template>

<script setup>
  import MemberInput from 'src/components/groups/forms/MemberInput.vue';
  import CsvUpload from 'src/components/common/uploads/csv/CsvUpload.vue';

  import {computed, ref} from 'vue';
  import {axiosFeathers, restCore} from 'src/components/common/uploads/services/utils';
  import {idGet} from 'src/utils/id-get';
  import {HFind} from 'src/utils/hFind';
  import {HQuery} from 'src/utils/hQuery';

  import {useGroups} from 'stores/groups';
  const store = useGroups();

  import {useOrgs} from 'stores/orgs';
  const orgStore = useOrgs();

  import {usePpls} from 'stores/ppls';
  import {$errNotify} from 'src/utils/global-methods';
  const pplsStore = usePpls();

  const props = defineProps({
    modelValue: { required: true },
    org: { required: false }
  })

  const { item:org } = idGet({
    value: computed(() => props.org || props.modelValue?.org),
    store: orgStore
  })

  const { item:group } = idGet({
    value: computed(() => props.modelValue),
    store
  })

  const response = ref({});

  const panel = ref('base');

  const requiredFields = ref([
    {
      field: 'email',
      v: ['email'],
      required: true
    }
  ]);

  const uploadList = async (val, meta) => {
    const res = await axiosFeathers().patch(`/groups/${props.modelValue._id}`, val, {
      params: {
        addMembers: meta,
        core: restCore()
      }
    });
    response.value = res.data?._fastjoin?.addMembers;
  };

  const exampleData = computed(() => {
    return [
      {
        header: 'Name',
        required: false,
        ex: 'Jane Doe'
      },
      {
        header: 'firstName',
        required: true,
        ex: 'Jane'
      },
      {
        header: 'lastName',
        required: true,
        ex: 'Doe'
      },
      {
        header: 'Email',
        required: true,
        ex: '<EMAIL>',
      },
      {
        header: 'Phone',
        ex: '(555)555-5555'
      },
      {
        header: 'DOB (MM/DD/YYYY)',
        ex: '01/01/1990'
      },
      {
        header: 'SSN',
        ex: '***********'
      },
      {
        header: 'Hire Date',
        ex: '01/01/2024'
      },
      {
        header: 'Comp Code',
        ex: `${org.value.name?.split(' ').join('_').toLowerCase()}:staff_accountant`
      }
    ];
  })

  const pauseSearch = ref(true);

  const loading = ref(false);

  const { searchQ, search } = HQuery({})
  const relatedOrgs = computed(() => {
    const list = [];
    for(const k in org.value?.controls || {}){
      for(const sk in org.value.controls[k].orgs || {}){
        list.push(sk);
      }
    }
    for(const k in org.value?.asg || {}){
      for(const sk in org.value.asg[k].orgs || {}){
        list.push(sk);
      }
    }
    return list;
  })
  const { h$ } = HFind({
    store: pplsStore,
    pause: pauseSearch,
    params: computed(() => {

      return {
        query: {
          ...searchQ.value,
          inOrgs: { $in: relatedOrgs.value }
        }
      }
    })
  })

  const chooseMember = async (val) => {
    loading.value = true;
    await store.patch(group.value._id, { $addToSet: { members: val._id }})
        .catch(err => $errNotify(`Could not add to group: ${err.message}`))
  }
</script>

<style lang="scss" scoped>
  .__gr {
    display: grid;
    grid-template-columns: auto 1fr;
    align-items: center;
  }
</style>
