<template>
  <div class="__cc_vid_popup">
    <div class="__btn" v-if="upload.url">
      <q-btn @click="emit('update:open', !open)" class="__bt" @pointerenter="hover = true" @pointerleave="hover = false"
             round :size="hover && !open ? 'md' : 'xs'" color="ir-off" text-color="ir-bg1">
        <q-icon :name="`${!open ? 'mdi-message-video' : 'mdi-close'}`"></q-icon>
      </q-btn>
      <div :class="`__menu ${open ? '' : '__menu_off'}`">
        <div class="__closer" v-if="open">
          <q-btn color="red" size="sm" dense flat icon="mdi-close" @click="emit('update:open', false)">
          </q-btn>
        </div>

        <div class="q-pa-sm" v-if="reF._id || person._id">
          <div class="_fw font-7-8r q-px-sm tw-six text-ir-deep">
            A message from
          </div>
          <div class="row items-center">
          <default-chip :model-value="reF._id ? reF : person"></default-chip>
            <q-space></q-space>
            <q-chip color="transparent" dense square clickable>
              <span class="tw-six font-3-4r">Dismiss</span>
              <remove-proxy remove-label="Done watching?" no-label="No" @remove="emit('done')"></remove-proxy>
            </q-chip>
          </div>

        </div>
        <div class="__vc">
          <q-video
              class="_fa"
              ratio="1.7777"
              :src="upload.url"
          ></q-video>
        </div>

      </div>
    </div>
  </div>
</template>

<script setup>
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';
  import RemoveProxy from 'components/common/buttons/RemoveProxy.vue';

  import {computed, ref} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useUploads} from 'stores/uploads';
  import {useRefs} from 'stores/refs';
  import {usePpls} from 'stores/ppls';
  import {useLogins} from 'stores/logins';

  const uploadStore = useUploads();
  const refStore = useRefs();
  const pplStore = usePpls();
  const loginStore = useLogins();

  const emit = defineEmits(['update:open', 'done']);
  const props = defineProps({
    open: Boolean,
    uploadId: String
  })
  const hover = ref(false);

  const { item: upload } = idGet({
    store: uploadStore,
    value: computed(() => props.uploadId)
  })

  const { item:reF } = idGet({
    store: refStore,
    value: computed(() => upload.value.ref)
  })

  const { item:login } = idGet({
    store: loginStore,
    value: computed(() => upload.value.ref ? undefined : upload.value.createdBy?.login),
    params: ref({ runJoin: { login_person: true }})
  })

  const { item:person } = idGet({
    store: pplStore,
    value: computed(() => login.value._fastjoin?.owner || login.value.owner)
  })


</script>

<style lang="scss" scoped>
  .__cc_vid_popup {
    position: fixed;
    z-index: 3000;
    bottom: max(2vw, 20px);
    left: max(2vw, 20px);

    .__btn {
      position: relative;
      overflow: visible;

      .__bt {
        transition: all .2s;

      }
    }

    .__menu {
      padding: 25px 15px 15px 15px;
      position: absolute;
      top: -10px;
      left: 0;
      height: 350px;
      max-height: 90vh;
      transform: translate(10px, -100%);
      background: rgba(255,255,255,.5);
      backdrop-filter: blur(3px);
      color: var(--ir-text);
      border-radius: 10px 10px 10px 0;
      width: 360px;
      box-shadow: 0 0 6px rgba(99, 99, 99, .4);
      transition: all .4s ease;
      overflow: hidden;
    }

    .__menu_off {
      max-height: 0;
      padding: 0;
      overflow: hidden;
      pointer-events: none;
    }

    .__closer {
      z-index: 2;
      position: absolute;
      top: 2px;
      right: 2px;
    }
  }

  @media screen and (max-width: 700px) {
    .__ims_popup {
      z-index: 19000;

      .__menu {
        border-radius: 0;
        transform: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        max-height: 100vh;
        z-index: 100;
      }

      .__menu_off {
        max-height: 0;
        padding: 0;
        overflow: hidden;
        pointer-events: none;
      }

      .__closer {
        z-index: 200;
        position: fixed;
        top: 2px;
        right: 2px;
      }
    }
  }

  .__vc {
    width: min(330px, calc(100vw - 30px));
    height: calc(min(330px, calc(100vw - 30px)) / 1.7777);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 8px var(--ir-light);
  }
</style>
