<template>
  <q-page>
    <div class="row justify-center mnh100 bg-ir-bg2">
      <div class="_xsent pd10 pw2 bg-white">
        <q-tab-panels v-model="tab" animated>
          <q-tab-panel class="_panel" name="add">
            <video-form @update:modelValue="handleUpload" v-model="upload" allow-url></video-form>
          </q-tab-panel>
          <q-tab-panel class="_panel" name="link">
            <div class="row q-pa-md">
              <q-chip color="transparent" clickable @click="tab = 'add'">
                <q-icon name="mdi-chevron-left" color="primary"></q-icon>
                <span class="q-ml-sm">Back</span>
              </q-chip>
            </div>

            <div class="q-py-sm">
              <div class="font-1r">Add any page URL to get your video embedded url</div>
              <q-input v-model="pasty" placeholder="Paste CommonCare url..."></q-input>
              <q-chip v-if="pasty" color="ir-bg2" clickable @click="$copyTextToClipboard(fullUrl)">
                <q-icon name="mdi-content-copy" class="q-mr-sm" color="a3"></q-icon>
                <span>{{fullUrl}}</span>
              </q-chip>
            </div>
            <div class="q-py-sm">
              <div class="font-1r">Add this to the end of any url to embed the video</div>
              <q-chip color="ir-bg2" clickable @click="$copyTextToClipboard(param)">
                <q-icon name="mdi-content-copy" class="q-mr-sm" color="a3"></q-icon>
                <span>{{param}}</span>
              </q-chip>
            </div>

          </q-tab-panel>
        </q-tab-panels>
      </div>
    </div>
  </q-page>
</template>

<script setup>
  import VideoForm from 'components/common/uploads/video/VideoForm.vue';
  import {computed, ref} from 'vue';
  import {$copyTextToClipboard} from 'src/utils/global-methods';
  import {useUploads} from 'stores/uploads';

  const uploadStore = useUploads();

  const upload = ref({})
  const tab = ref('add');
  const param = computed(() => upload.value._id ? `?ccvideo=${upload.value._id}` : '')
  const pasty = ref('');
  const fullUrl = computed(() => pasty.value + param.value)

  const handleUpload = async (val) => {
    if (val._id) upload.value = val;
    else {
      const data = {
        url: val.url,
        fileId: `video-link-${new Date().getTime()}`,
        storage: 'video-link',
        name: val.title
      }
      const created = await uploadStore.create(data)
      if (created._id) upload.value = created;
    }
    tab.value = 'link';
  }
</script>

<style lang="scss" scoped>

</style>
